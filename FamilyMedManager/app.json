{"expo": {"name": "FamilyMedManager", "slug": "FamilyMedManager", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "familymedmanager", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"bundleIdentifier": "com.magizhdevelopment.familymedmanager", "supportsTablet": true}, "android": {"adaptiveIcon": {"backgroundColor": "#E6F4FE", "foregroundImage": "./assets/images/android-icon-foreground.png", "backgroundImage": "./assets/images/android-icon-background.png", "monochromeImage": "./assets/images/android-icon-monochrome.png"}, "edgeToEdgeEnabled": true, "predictiveBackGestureEnabled": false}, "web": {"output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/illustration.png", "resizeMode": "contain", "backgroundColor": "#5B7FE5"}], "expo-sqlite"], "experiments": {"typedRoutes": true, "reactCompiler": true}, "extra": {"router": {}, "eas": {"projectId": "5e87a0c6-295f-427c-bc2d-f21a68b66f30"}}}}